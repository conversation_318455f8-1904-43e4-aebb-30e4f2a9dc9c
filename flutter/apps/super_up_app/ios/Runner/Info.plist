<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>Orbit</string>
		<key>CFBundleDocumentTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeName</key>
				<string>ShareHandler</string>
				<key>LSHandlerRank</key>
				<string>Alternate</string>
				<key>LSItemContentTypes</key>
				<array>
					<string>public.file-url</string>
					<string>public.image</string>
					<string>public.text</string>
					<string>public.movie</string>
					<string>public.url</string>
					<string>public.data</string>
				</array>
			</dict>
		</array>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleVersion</key>
		<string>59</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>Orbit</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>1.5.4</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>ShareMedia-$(PRODUCT_BUNDLE_IDENTIFIER)</string>
				</array>
			</dict>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLName</key>
				<string>orbit.profile</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>orbit</string>
					<string>https</string>
				</array>
			</dict>
		</array>
		<key>GADApplicationIdentifier</key>
		<string>ca-app-pub-5480704135551772~7567166699</string>
		<key>GoogleUtilitiesAppDelegateProxyEnabled</key>
		<false/>
		<key>com.apple.developer.associated-domains</key>
		<array>
			<string>applinks:api.orbit.ke</string>
			<string>applinks:orbit.ke</string>
		</array>
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>comgooglemaps</string>
			<string>baidumap</string>
			<string>iosamap</string>
			<string>waze</string>
			<string>yandexmaps</string>
			<string>yandexnavi</string>
			<string>citymapper</string>
			<string>mapswithme</string>
			<string>osmandmaps</string>
			<string>dgis</string>
			<string>qqmap</string>
			<string>here-location</string>
			<string>tomtomgo</string>
		</array>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>LSSupportsOpeningDocumentsInPlace</key>
		<true/>
		<key>UIFileSharingEnabled</key>
		<true/>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSAllowsArbitraryLoadsInWebContent</key>
		<true/>
		<key>NSCameraUsageDescription</key>
		<string>to allow user to take photo and video and upload it to server</string>
		<key>NSLocalNetworkUsageDescription</key>
		<string>to allow user to connect with server server</string>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>To allow send current location pin in chats</string>
		<key>NSLocationAlwaysUsageDescription</key>
		<string>The app needs access to location when in the background to send inside app messages with the new real location.</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>The app needs access to location to send in chat.</string>
		<key>NSMicrophoneUsageDescription</key>
		<string>to allow user to take video with voice for more info and send it to the peer user and upload it to server</string>
		<key>NSPhotoLibraryAddUsageDescription</key>
		<string>Super-up would like to save photos from the app chat to your gallery</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>To allow user to select photo or photos share with his friends in chats and upload it to server</string>
		<key>NSUserActivityTypes</key>
		<array>
			<string>INSendMessageIntent</string>
		</array>
		<key>SKAdNetworkItems</key>
		<array>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>cstr6suwn9.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>4fzdc2evr5.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>4pfyvq9l8r.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>2fnua5tdw4.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>ydx93a7ass.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>5a6flpkh64.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>p78axxw29g.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>v72qych5uu.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>ludvb6z3bs.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>cp8zw746q7.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>3sh42y64q3.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>c6k4g5qg8m.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>s39g8k73mm.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>3qy4746246.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>f38h382jlk.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>hs6bdukanm.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>v4nxqhlyqp.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>wzmmz9fp6w.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>yclnxrl5pm.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>t38b2kh725.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>7ug5zh24hu.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>gta9lk7p23.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>vutu7akeur.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>y5ghdn5j9k.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>n6fk4nfna4.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>v9wttpbfk9.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>n38lu8286q.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>47vhws6wlr.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>kbd757ywx3.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>9t245vhmpl.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>eh6m2bh4zr.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>a2p9lx4jpn.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>22mmun2rn5.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>4468km3ulz.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>2u9pt9hc89.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>8s468mfl3y.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>klf5c3l5u5.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>ppxm28t8ap.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>ecpz2srf59.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>uw77j35x4d.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>pwa73g5rt2.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>mlmmfzh3r3.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>578prtvx9j.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>4dzt52r2t5.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>e5fvkxwrpn.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>8c4e2ghe7u.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>zq492l623r.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>3rd42ekr43.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>3qcr597p9d.skadnetwork</string>
			</dict>
		</array>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<key>UIBackgroundModes</key>
		<array>
			<string>fetch</string>
			<string>remote-notification</string>
			<string>voip</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UIStatusBarHidden</key>
		<false/>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false/>
		<key>ITSAppUsesNonExemptEncryption</key>
		<false/>
	</dict>
</plist>
